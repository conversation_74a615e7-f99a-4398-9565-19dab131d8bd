
# Introduction

Welcome to the documentation for Unsplasharp, a modern, asynchronous, and feature-rich .NET library for interacting with the [Unsplash API](https://unsplash.com/developers).

## What is Unsplasharp?

Unsplasharp is a .NET Standard 2.0 library that provides a convenient and easy-to-use wrapper around the Unsplash API. It allows you to easily integrate Unsplash's vast library of high-quality photos into your .NET applications.

## Features

- **Modern and Asynchronous**: Built with `async/await` for non-blocking I/O.
- **Comprehensive Error Handling**: Provides specific exception types for different error scenarios.
- **IHttpClientFactory Integration**: Supports `IHttpClientFactory` for efficient management of `HttpClient` instances.
- **Structured Logging**: Integrates with `Microsoft.Extensions.Logging` for detailed insights.
- **System.Text.Json**: Uses the high-performance `System.Text.Json` for JSON serialization.
- **Full API Coverage**: Provides access to all of the Unsplash API endpoints.

## Getting Help

If you need help or have any questions, please feel free to [open an issue](https://github.com/rootasjey/unsplasharp/issues) on GitHub.
