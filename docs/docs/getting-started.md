
# Getting Started

This guide will walk you through the process of using Unsplasharp in your .NET application.

## Installation

Unsplasharp is available on NuGet. You can install it using the .NET CLI:

```bash
dotnet add package Unsplasharp
```

## Quickstart

Here's a simple example of how to use Un<PERSON>lasharp to get a random photo:

```csharp
using Unsplasharp;

// Create a new client with your Application ID
var client = new UnsplasharpClient("YOUR_APPLICATION_ID");

// Get a random photo
var photo = await client.GetRandomPhoto();

// Print the photo's URL
Console.WriteLine(photo?.Urls?.Regular);
```

## API Examples

### Search for Photos

```csharp
var photos = await client.SearchPhotos("nature");
foreach (var photo in photos)
{
    Console.WriteLine(photo.Urls.Small);
}
```

### Get a Specific Photo

```csharp
var photo = await client.GetPhoto("photo-id");
Console.WriteLine(photo.Description);
```

### Get a User's Profile

```csharp
var user = await client.GetUser("username");
Console.WriteLine(user.Name);
```

For more detailed information on the available methods, please refer to the API documentation.
