{"$schema": "https://raw.githubusercontent.com/dotnet/docfx/main/schemas/docfx.schema.json", "metadata": [{"src": [{"src": "../src/unsplasharp", "files": ["**/*.c<PERSON><PERSON>j"]}], "dest": "api"}], "build": {"content": [{"files": ["**/*.{md,yml}"], "exclude": ["_site/**"]}], "resource": [{"files": ["images/**"]}], "output": "_site", "template": ["default", "modern"], "globalMetadata": {"_appName": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_appTitle": "Unsplasharp Documentation", "_enableSearch": true, "pdf": true}}}