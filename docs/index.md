---
_layout: landing
---

# Unsplasharp

A modern, asynchronous, and feature-rich .NET library for the [Unsplash API](https://unsplash.com/developers).

## Get Started

To get started with Unsplasharp, check out the [Getting Started](~/docs/getting-started.md) guide.

## Features

- **Modern and Asynchronous**: Built with `async/await` for non-blocking I/O.
- **Comprehensive Error Handling**: Provides specific exception types for different error scenarios.
- **IHttpClientFactory Integration**: Supports `IHttpClientFactory` for efficient management of `HttpClient` instances.
- **Structured Logging**: Integrates with `Microsoft.Extensions.Logging` for detailed insights.
- **System.Text.Json**: Uses the high-performance `System.Text.Json` for JSON serialization.
- **Full API Coverage**: Provides access to all of the Unsplash API endpoints.

## Contributing

Unsplasharp is an open-source project. Contributions are welcome! Please see the [contribution guidelines](https://github.com/rootasjey/unsplasharp/blob/main/CONTRIBUTING.md) for more information.