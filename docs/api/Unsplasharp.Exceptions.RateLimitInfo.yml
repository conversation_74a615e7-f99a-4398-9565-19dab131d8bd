### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Exceptions.RateLimitInfo
  commentId: T:Unsplasharp.Exceptions.RateLimitInfo
  id: RateLimitInfo
  parent: Unsplasharp.Exceptions
  children:
  - Unsplasharp.Exceptions.RateLimitInfo.#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset})
  - Unsplasharp.Exceptions.RateLimitInfo.FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)
  - Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
  - Unsplasharp.Exceptions.RateLimitInfo.Limit
  - Unsplasharp.Exceptions.RateLimitInfo.Remaining
  - Unsplasharp.Exceptions.RateLimitInfo.Reset
  - Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
  langs:
  - csharp
  - vb
  name: RateLimitInfo
  nameWithType: RateLimitInfo
  fullName: Unsplasharp.Exceptions.RateLimitInfo
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: RateLimitInfo
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 232
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Contains rate limit information extracted from HTTP headers
  example: []
  syntax:
    content: public class RateLimitInfo
    content.vb: Public Class RateLimitInfo
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Exceptions.RateLimitInfo.Limit
  commentId: P:Unsplasharp.Exceptions.RateLimitInfo.Limit
  id: Limit
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: Limit
  nameWithType: RateLimitInfo.Limit
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Limit
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Limit
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 237
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: The maximum number of requests allowed per hour
  example: []
  syntax:
    content: public int? Limit { get; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public ReadOnly Property Limit As Integer?
  overload: Unsplasharp.Exceptions.RateLimitInfo.Limit*
- uid: Unsplasharp.Exceptions.RateLimitInfo.Remaining
  commentId: P:Unsplasharp.Exceptions.RateLimitInfo.Remaining
  id: Remaining
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: Remaining
  nameWithType: RateLimitInfo.Remaining
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Remaining
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Remaining
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 242
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: The number of requests remaining in the current hour
  example: []
  syntax:
    content: public int? Remaining { get; }
    parameters: []
    return:
      type: System.Nullable{System.Int32}
    content.vb: Public ReadOnly Property Remaining As Integer?
  overload: Unsplasharp.Exceptions.RateLimitInfo.Remaining*
- uid: Unsplasharp.Exceptions.RateLimitInfo.Reset
  commentId: P:Unsplasharp.Exceptions.RateLimitInfo.Reset
  id: Reset
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: Reset
  nameWithType: RateLimitInfo.Reset
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Reset
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Reset
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 247
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: When the rate limit resets
  example: []
  syntax:
    content: public DateTimeOffset? Reset { get; }
    parameters: []
    return:
      type: System.Nullable{System.DateTimeOffset}
    content.vb: Public ReadOnly Property Reset As DateTimeOffset?
  overload: Unsplasharp.Exceptions.RateLimitInfo.Reset*
- uid: Unsplasharp.Exceptions.RateLimitInfo.#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset})
  commentId: M:Unsplasharp.Exceptions.RateLimitInfo.#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset})
  id: '#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset})'
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: RateLimitInfo(int?, int?, DateTimeOffset?)
  nameWithType: RateLimitInfo.RateLimitInfo(int?, int?, DateTimeOffset?)
  fullName: Unsplasharp.Exceptions.RateLimitInfo.RateLimitInfo(int?, int?, System.DateTimeOffset?)
  type: Constructor
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: .ctor
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 255
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Initializes a new instance of the RateLimitInfo class
  example: []
  syntax:
    content: public RateLimitInfo(int? limit, int? remaining, DateTimeOffset? reset)
    parameters:
    - id: limit
      type: System.Nullable{System.Int32}
      description: The rate limit
    - id: remaining
      type: System.Nullable{System.Int32}
      description: The remaining requests
    - id: reset
      type: System.Nullable{System.DateTimeOffset}
      description: When the rate limit resets
    content.vb: Public Sub New(limit As Integer?, remaining As Integer?, reset As DateTimeOffset?)
  overload: Unsplasharp.Exceptions.RateLimitInfo.#ctor*
  nameWithType.vb: RateLimitInfo.New(Integer?, Integer?, DateTimeOffset?)
  fullName.vb: Unsplasharp.Exceptions.RateLimitInfo.New(Integer?, Integer?, System.DateTimeOffset?)
  name.vb: New(Integer?, Integer?, DateTimeOffset?)
- uid: Unsplasharp.Exceptions.RateLimitInfo.FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)
  commentId: M:Unsplasharp.Exceptions.RateLimitInfo.FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)
  id: FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: FromHeaders(HttpResponseHeaders)
  nameWithType: RateLimitInfo.FromHeaders(HttpResponseHeaders)
  fullName: Unsplasharp.Exceptions.RateLimitInfo.FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)
  type: Method
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: FromHeaders
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 267
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Extracts rate limit information from HTTP response headers
  example: []
  syntax:
    content: public static RateLimitInfo? FromHeaders(HttpResponseHeaders headers)
    parameters:
    - id: headers
      type: System.Net.Http.Headers.HttpResponseHeaders
      description: The HTTP response headers
    return:
      type: Unsplasharp.Exceptions.RateLimitInfo
      description: A RateLimitInfo instance, or null if no rate limit headers found
    content.vb: Public Shared Function FromHeaders(headers As HttpResponseHeaders) As RateLimitInfo
  overload: Unsplasharp.Exceptions.RateLimitInfo.FromHeaders*
- uid: Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
  commentId: P:Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
  id: IsExceeded
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: IsExceeded
  nameWithType: RateLimitInfo.IsExceeded
  fullName: Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: IsExceeded
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 312
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Indicates whether the rate limit has been exceeded
  example: []
  syntax:
    content: public bool IsExceeded { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsExceeded As Boolean
  overload: Unsplasharp.Exceptions.RateLimitInfo.IsExceeded*
- uid: Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
  commentId: P:Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
  id: TimeUntilReset
  parent: Unsplasharp.Exceptions.RateLimitInfo
  langs:
  - csharp
  - vb
  name: TimeUntilReset
  nameWithType: RateLimitInfo.TimeUntilReset
  fullName: Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/ErrorContext.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: TimeUntilReset
    path: ../src/unsplasharp/Source/Exceptions/ErrorContext.cs
    startLine: 317
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Gets the time until the rate limit resets
  example: []
  syntax:
    content: public TimeSpan? TimeUntilReset { get; }
    parameters: []
    return:
      type: System.Nullable{System.TimeSpan}
    content.vb: Public ReadOnly Property TimeUntilReset As TimeSpan?
  overload: Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset*
references:
- uid: Unsplasharp.Exceptions
  commentId: N:Unsplasharp.Exceptions
  href: Unsplasharp.html
  name: Unsplasharp.Exceptions
  nameWithType: Unsplasharp.Exceptions
  fullName: Unsplasharp.Exceptions
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Exceptions
    name: Exceptions
    href: Unsplasharp.Exceptions.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Exceptions
    name: Exceptions
    href: Unsplasharp.Exceptions.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Exceptions.RateLimitInfo.Limit*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.Limit
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_Limit
  name: Limit
  nameWithType: RateLimitInfo.Limit
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Limit
- uid: System.Nullable{System.Int32}
  commentId: T:System.Nullable{System.Int32}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int?
  nameWithType: int?
  fullName: int?
  nameWithType.vb: Integer?
  fullName.vb: Integer?
  name.vb: Integer?
  spec.csharp:
  - uid: System.Int32
    name: int
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
  spec.vb:
  - uid: System.Int32
    name: Integer
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.int32
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: Unsplasharp.Exceptions.RateLimitInfo.Remaining*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.Remaining
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_Remaining
  name: Remaining
  nameWithType: RateLimitInfo.Remaining
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Remaining
- uid: Unsplasharp.Exceptions.RateLimitInfo.Reset*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.Reset
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_Reset
  name: Reset
  nameWithType: RateLimitInfo.Reset
  fullName: Unsplasharp.Exceptions.RateLimitInfo.Reset
- uid: System.Nullable{System.DateTimeOffset}
  commentId: T:System.Nullable{System.DateTimeOffset}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.datetimeoffset
  name: DateTimeOffset?
  nameWithType: DateTimeOffset?
  fullName: System.DateTimeOffset?
  spec.csharp:
  - uid: System.DateTimeOffset
    name: DateTimeOffset
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.datetimeoffset
  - name: '?'
  spec.vb:
  - uid: System.DateTimeOffset
    name: DateTimeOffset
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.datetimeoffset
  - name: '?'
- uid: Unsplasharp.Exceptions.RateLimitInfo.#ctor*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.#ctor
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo__ctor_System_Nullable_System_Int32__System_Nullable_System_Int32__System_Nullable_System_DateTimeOffset__
  name: RateLimitInfo
  nameWithType: RateLimitInfo.RateLimitInfo
  fullName: Unsplasharp.Exceptions.RateLimitInfo.RateLimitInfo
  nameWithType.vb: RateLimitInfo.New
  fullName.vb: Unsplasharp.Exceptions.RateLimitInfo.New
  name.vb: New
- uid: Unsplasharp.Exceptions.RateLimitInfo.FromHeaders*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.FromHeaders
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_FromHeaders_System_Net_Http_Headers_HttpResponseHeaders_
  name: FromHeaders
  nameWithType: RateLimitInfo.FromHeaders
  fullName: Unsplasharp.Exceptions.RateLimitInfo.FromHeaders
- uid: System.Net.Http.Headers.HttpResponseHeaders
  commentId: T:System.Net.Http.Headers.HttpResponseHeaders
  parent: System.Net.Http.Headers
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.net.http.headers.httpresponseheaders
  name: HttpResponseHeaders
  nameWithType: HttpResponseHeaders
  fullName: System.Net.Http.Headers.HttpResponseHeaders
- uid: Unsplasharp.Exceptions.RateLimitInfo
  commentId: T:Unsplasharp.Exceptions.RateLimitInfo
  parent: Unsplasharp.Exceptions
  href: Unsplasharp.Exceptions.RateLimitInfo.html
  name: RateLimitInfo
  nameWithType: RateLimitInfo
  fullName: Unsplasharp.Exceptions.RateLimitInfo
- uid: System.Net.Http.Headers
  commentId: N:System.Net.Http.Headers
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Net.Http.Headers
  nameWithType: System.Net.Http.Headers
  fullName: System.Net.Http.Headers
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Net
    name: Net
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net
  - name: .
  - uid: System.Net.Http
    name: Http
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http
  - name: .
  - uid: System.Net.Http.Headers
    name: Headers
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.headers
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Net
    name: Net
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net
  - name: .
  - uid: System.Net.Http
    name: Http
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http
  - name: .
  - uid: System.Net.Http.Headers
    name: Headers
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.headers
- uid: Unsplasharp.Exceptions.RateLimitInfo.IsExceeded*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_IsExceeded
  name: IsExceeded
  nameWithType: RateLimitInfo.IsExceeded
  fullName: Unsplasharp.Exceptions.RateLimitInfo.IsExceeded
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset*
  commentId: Overload:Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
  href: Unsplasharp.Exceptions.RateLimitInfo.html#Unsplasharp_Exceptions_RateLimitInfo_TimeUntilReset
  name: TimeUntilReset
  nameWithType: RateLimitInfo.TimeUntilReset
  fullName: Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset
- uid: System.Nullable{System.TimeSpan}
  commentId: T:System.Nullable{System.TimeSpan}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.timespan
  name: TimeSpan?
  nameWithType: TimeSpan?
  fullName: System.TimeSpan?
  spec.csharp:
  - uid: System.TimeSpan
    name: TimeSpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.timespan
  - name: '?'
  spec.vb:
  - uid: System.TimeSpan
    name: TimeSpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.timespan
  - name: '?'
