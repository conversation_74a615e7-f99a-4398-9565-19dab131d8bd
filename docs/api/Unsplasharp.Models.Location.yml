### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Models.Location
  commentId: T:Unsplasharp.Models.Location
  id: Location
  parent: Unsplasharp.Models
  children:
  - Unsplasharp.Models.Location.City
  - Unsplasharp.Models.Location.Country
  - Unsplasharp.Models.Location.Name
  - Unsplasharp.Models.Location.Position
  - Unsplasharp.Models.Location.Title
  langs:
  - csharp
  - vb
  name: Location
  nameWithType: Location
  fullName: Unsplasharp.Models.Location
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Location
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 270
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: The photo's location.
  example: []
  syntax:
    content: public class Location
    content.vb: Public Class Location
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Models.Location.Title
  commentId: P:Unsplasharp.Models.Location.Title
  id: Title
  parent: Unsplasharp.Models.Location
  langs:
  - csharp
  - vb
  name: Title
  nameWithType: Location.Title
  fullName: Unsplasharp.Models.Location.Title
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Title
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 276
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Full location's name (district + city + country (if available))
  example: []
  syntax:
    content: public string Title { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Title As String
  overload: Unsplasharp.Models.Location.Title*
- uid: Unsplasharp.Models.Location.Name
  commentId: P:Unsplasharp.Models.Location.Name
  id: Name
  parent: Unsplasharp.Models.Location
  langs:
  - csharp
  - vb
  name: Name
  nameWithType: Location.Name
  fullName: Unsplasharp.Models.Location.Name
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Name
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 286
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Location's name
  example: []
  syntax:
    content: public string Name { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Name As String
  overload: Unsplasharp.Models.Location.Name*
- uid: Unsplasharp.Models.Location.City
  commentId: P:Unsplasharp.Models.Location.City
  id: City
  parent: Unsplasharp.Models.Location
  langs:
  - csharp
  - vb
  name: City
  nameWithType: Location.City
  fullName: Unsplasharp.Models.Location.City
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: City
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 296
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Location’s city.
  example: []
  syntax:
    content: public string City { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property City As String
  overload: Unsplasharp.Models.Location.City*
- uid: Unsplasharp.Models.Location.Country
  commentId: P:Unsplasharp.Models.Location.Country
  id: Country
  parent: Unsplasharp.Models.Location
  langs:
  - csharp
  - vb
  name: Country
  nameWithType: Location.Country
  fullName: Unsplasharp.Models.Location.Country
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Country
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 306
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Location’s country.
  example: []
  syntax:
    content: public string Country { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Country As String
  overload: Unsplasharp.Models.Location.Country*
- uid: Unsplasharp.Models.Location.Position
  commentId: P:Unsplasharp.Models.Location.Position
  id: Position
  parent: Unsplasharp.Models.Location
  langs:
  - csharp
  - vb
  name: Position
  nameWithType: Location.Position
  fullName: Unsplasharp.Models.Location.Position
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Position
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 316
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Location’s position (latitude, longitude).
  example: []
  syntax:
    content: public Position Position { get; set; }
    parameters: []
    return:
      type: Unsplasharp.Models.Position
    content.vb: Public Property Position As Position
  overload: Unsplasharp.Models.Location.Position*
references:
- uid: Unsplasharp.Models
  commentId: N:Unsplasharp.Models
  href: Unsplasharp.html
  name: Unsplasharp.Models
  nameWithType: Unsplasharp.Models
  fullName: Unsplasharp.Models
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Models.Location.Title*
  commentId: Overload:Unsplasharp.Models.Location.Title
  href: Unsplasharp.Models.Location.html#Unsplasharp_Models_Location_Title
  name: Title
  nameWithType: Location.Title
  fullName: Unsplasharp.Models.Location.Title
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Models.Location.Name*
  commentId: Overload:Unsplasharp.Models.Location.Name
  href: Unsplasharp.Models.Location.html#Unsplasharp_Models_Location_Name
  name: Name
  nameWithType: Location.Name
  fullName: Unsplasharp.Models.Location.Name
- uid: Unsplasharp.Models.Location.City*
  commentId: Overload:Unsplasharp.Models.Location.City
  href: Unsplasharp.Models.Location.html#Unsplasharp_Models_Location_City
  name: City
  nameWithType: Location.City
  fullName: Unsplasharp.Models.Location.City
- uid: Unsplasharp.Models.Location.Country*
  commentId: Overload:Unsplasharp.Models.Location.Country
  href: Unsplasharp.Models.Location.html#Unsplasharp_Models_Location_Country
  name: Country
  nameWithType: Location.Country
  fullName: Unsplasharp.Models.Location.Country
- uid: Unsplasharp.Models.Location.Position*
  commentId: Overload:Unsplasharp.Models.Location.Position
  href: Unsplasharp.Models.Location.html#Unsplasharp_Models_Location_Position
  name: Position
  nameWithType: Location.Position
  fullName: Unsplasharp.Models.Location.Position
- uid: Unsplasharp.Models.Position
  commentId: T:Unsplasharp.Models.Position
  parent: Unsplasharp.Models
  href: Unsplasharp.Models.Position.html
  name: Position
  nameWithType: Position
  fullName: Unsplasharp.Models.Position
