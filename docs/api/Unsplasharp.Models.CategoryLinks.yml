### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Models.CategoryLinks
  commentId: T:Unsplasharp.Models.CategoryLinks
  id: CategoryLinks
  parent: Unsplasharp.Models
  children:
  - Unsplasharp.Models.CategoryLinks.Photos
  - Unsplasharp.Models.CategoryLinks.Self
  langs:
  - csharp
  - vb
  name: CategoryLinks
  nameWithType: CategoryLinks
  fullName: Unsplasharp.Models.CategoryLinks
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: CategoryLinks
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 411
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Photo's link to its categories.
  example: []
  syntax:
    content: public class CategoryLinks
    content.vb: Public Class CategoryLinks
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Models.CategoryLinks.Self
  commentId: P:Unsplasharp.Models.CategoryLinks.Self
  id: Self
  parent: Unsplasharp.Models.CategoryLinks
  langs:
  - csharp
  - vb
  name: Self
  nameWithType: CategoryLinks.Self
  fullName: Unsplasharp.Models.CategoryLinks.Self
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Self
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 415
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Link of this category.
  example: []
  syntax:
    content: public string Self { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Self As String
  overload: Unsplasharp.Models.CategoryLinks.Self*
- uid: Unsplasharp.Models.CategoryLinks.Photos
  commentId: P:Unsplasharp.Models.CategoryLinks.Photos
  id: Photos
  parent: Unsplasharp.Models.CategoryLinks
  langs:
  - csharp
  - vb
  name: Photos
  nameWithType: CategoryLinks.Photos
  fullName: Unsplasharp.Models.CategoryLinks.Photos
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Photos
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 420
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Link to all photos in this category.
  example: []
  syntax:
    content: public string Photos { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Photos As String
  overload: Unsplasharp.Models.CategoryLinks.Photos*
references:
- uid: Unsplasharp.Models
  commentId: N:Unsplasharp.Models
  href: Unsplasharp.html
  name: Unsplasharp.Models
  nameWithType: Unsplasharp.Models
  fullName: Unsplasharp.Models
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Models.CategoryLinks.Self*
  commentId: Overload:Unsplasharp.Models.CategoryLinks.Self
  href: Unsplasharp.Models.CategoryLinks.html#Unsplasharp_Models_CategoryLinks_Self
  name: Self
  nameWithType: CategoryLinks.Self
  fullName: Unsplasharp.Models.CategoryLinks.Self
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Models.CategoryLinks.Photos*
  commentId: Overload:Unsplasharp.Models.CategoryLinks.Photos
  href: Unsplasharp.Models.CategoryLinks.html#Unsplasharp_Models_CategoryLinks_Photos
  name: Photos
  nameWithType: CategoryLinks.Photos
  fullName: Unsplasharp.Models.CategoryLinks.Photos
