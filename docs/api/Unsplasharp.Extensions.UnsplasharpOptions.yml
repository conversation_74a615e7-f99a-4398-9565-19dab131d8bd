### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Extensions.UnsplasharpOptions
  commentId: T:Unsplasharp.Extensions.UnsplasharpOptions
  id: UnsplasharpOptions
  parent: Unsplasharp.Extensions
  children:
  - Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
  - Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
  - Unsplasharp.Extensions.UnsplasharpOptions.Secret
  langs:
  - csharp
  - vb
  name: UnsplasharpOptions
  nameWithType: UnsplasharpOptions
  fullName: Unsplasharp.Extensions.UnsplasharpOptions
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: UnsplasharpOptions
    path: ../src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
    startLine: 84
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Extensions
  summary: Configuration options for Unsplasharp
  example: []
  syntax:
    content: public class UnsplasharpOptions
    content.vb: Public Class UnsplasharpOptions
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
  commentId: P:Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
  id: ApplicationId
  parent: Unsplasharp.Extensions.UnsplasharpOptions
  langs:
  - csharp
  - vb
  name: ApplicationId
  nameWithType: UnsplasharpOptions.ApplicationId
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: ApplicationId
    path: ../src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
    startLine: 89
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Extensions
  summary: Unsplash application ID (required)
  example: []
  syntax:
    content: public string ApplicationId { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property ApplicationId As String
  overload: Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId*
- uid: Unsplasharp.Extensions.UnsplasharpOptions.Secret
  commentId: P:Unsplasharp.Extensions.UnsplasharpOptions.Secret
  id: Secret
  parent: Unsplasharp.Extensions.UnsplasharpOptions
  langs:
  - csharp
  - vb
  name: Secret
  nameWithType: UnsplasharpOptions.Secret
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.Secret
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Secret
    path: ../src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
    startLine: 94
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Extensions
  summary: Optional secret for authenticated requests
  example: []
  syntax:
    content: public string? Secret { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Secret As String
  overload: Unsplasharp.Extensions.UnsplasharpOptions.Secret*
- uid: Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
  commentId: P:Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
  id: ConfigureHttpClient
  parent: Unsplasharp.Extensions.UnsplasharpOptions
  langs:
  - csharp
  - vb
  name: ConfigureHttpClient
  nameWithType: UnsplasharpOptions.ConfigureHttpClient
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: ConfigureHttpClient
    path: ../src/unsplasharp/Source/Extensions/ServiceCollectionExtensions.cs
    startLine: 99
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Extensions
  summary: Optional action to configure the HttpClient
  example: []
  syntax:
    content: public Action<HttpClient>? ConfigureHttpClient { get; set; }
    parameters: []
    return:
      type: System.Action{System.Net.Http.HttpClient}
    content.vb: Public Property ConfigureHttpClient As Action(Of HttpClient)
  overload: Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient*
references:
- uid: Unsplasharp.Extensions
  commentId: N:Unsplasharp.Extensions
  href: Unsplasharp.html
  name: Unsplasharp.Extensions
  nameWithType: Unsplasharp.Extensions
  fullName: Unsplasharp.Extensions
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Extensions
    name: Extensions
    href: Unsplasharp.Extensions.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Extensions
    name: Extensions
    href: Unsplasharp.Extensions.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId*
  commentId: Overload:Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
  href: Unsplasharp.Extensions.UnsplasharpOptions.html#Unsplasharp_Extensions_UnsplasharpOptions_ApplicationId
  name: ApplicationId
  nameWithType: UnsplasharpOptions.ApplicationId
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Extensions.UnsplasharpOptions.Secret*
  commentId: Overload:Unsplasharp.Extensions.UnsplasharpOptions.Secret
  href: Unsplasharp.Extensions.UnsplasharpOptions.html#Unsplasharp_Extensions_UnsplasharpOptions_Secret
  name: Secret
  nameWithType: UnsplasharpOptions.Secret
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.Secret
- uid: Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient*
  commentId: Overload:Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
  href: Unsplasharp.Extensions.UnsplasharpOptions.html#Unsplasharp_Extensions_UnsplasharpOptions_ConfigureHttpClient
  name: ConfigureHttpClient
  nameWithType: UnsplasharpOptions.ConfigureHttpClient
  fullName: Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient
- uid: System.Action{System.Net.Http.HttpClient}
  commentId: T:System.Action{System.Net.Http.HttpClient}
  parent: System
  definition: System.Action`1
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<HttpClient>
  nameWithType: Action<HttpClient>
  fullName: System.Action<System.Net.Http.HttpClient>
  nameWithType.vb: Action(Of HttpClient)
  fullName.vb: System.Action(Of System.Net.Http.HttpClient)
  name.vb: Action(Of HttpClient)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - uid: System.Net.Http.HttpClient
    name: HttpClient
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httpclient
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - uid: System.Net.Http.HttpClient
    name: HttpClient
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httpclient
  - name: )
- uid: System.Action`1
  commentId: T:System.Action`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.action-1
  name: Action<T>
  nameWithType: Action<T>
  fullName: System.Action<T>
  nameWithType.vb: Action(Of T)
  fullName.vb: System.Action(Of T)
  name.vb: Action(Of T)
  spec.csharp:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Action`1
    name: Action
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.action-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
