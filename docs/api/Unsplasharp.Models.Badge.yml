### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Models.Badge
  commentId: T:Unsplasharp.Models.Badge
  id: Badge
  parent: Unsplasharp.Models
  children:
  - Unsplasharp.Models.Badge.Link
  - Unsplasharp.Models.Badge.Primary
  - Unsplasharp.Models.Badge.Slug
  - Unsplasharp.Models.Badge.Title
  langs:
  - csharp
  - vb
  name: Badge
  nameWithType: Badge
  fullName: Unsplasharp.Models.Badge
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Models/User.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Badge
    path: ../src/unsplasharp/Source/Models/User.cs
    startLine: 260
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Badge (a user specific role).
  example: []
  syntax:
    content: public class Badge
    content.vb: Public Class Badge
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Models.Badge.Title
  commentId: P:Unsplasharp.Models.Badge.Title
  id: Title
  parent: Unsplasharp.Models.Badge
  langs:
  - csharp
  - vb
  name: Title
  nameWithType: Badge.Title
  fullName: Unsplasharp.Models.Badge.Title
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/User.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Title
    path: ../src/unsplasharp/Source/Models/User.cs
    startLine: 264
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Badge's title (e.g. book contributor).
  example: []
  syntax:
    content: public string Title { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Title As String
  overload: Unsplasharp.Models.Badge.Title*
- uid: Unsplasharp.Models.Badge.Primary
  commentId: P:Unsplasharp.Models.Badge.Primary
  id: Primary
  parent: Unsplasharp.Models.Badge
  langs:
  - csharp
  - vb
  name: Primary
  nameWithType: Badge.Primary
  fullName: Unsplasharp.Models.Badge.Primary
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/User.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Primary
    path: ../src/unsplasharp/Source/Models/User.cs
    startLine: 269
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: True if it's the primary badge.
  example: []
  syntax:
    content: public bool Primary { get; set; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public Property Primary As Boolean
  overload: Unsplasharp.Models.Badge.Primary*
- uid: Unsplasharp.Models.Badge.Slug
  commentId: P:Unsplasharp.Models.Badge.Slug
  id: Slug
  parent: Unsplasharp.Models.Badge
  langs:
  - csharp
  - vb
  name: Slug
  nameWithType: Badge.Slug
  fullName: Unsplasharp.Models.Badge.Slug
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/User.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Slug
    path: ../src/unsplasharp/Source/Models/User.cs
    startLine: 274
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Badge's description.
  example: []
  syntax:
    content: public string Slug { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Slug As String
  overload: Unsplasharp.Models.Badge.Slug*
- uid: Unsplasharp.Models.Badge.Link
  commentId: P:Unsplasharp.Models.Badge.Link
  id: Link
  parent: Unsplasharp.Models.Badge
  langs:
  - csharp
  - vb
  name: Link
  nameWithType: Badge.Link
  fullName: Unsplasharp.Models.Badge.Link
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/User.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Link
    path: ../src/unsplasharp/Source/Models/User.cs
    startLine: 279
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Badge's page's link.
  example: []
  syntax:
    content: public string Link { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Link As String
  overload: Unsplasharp.Models.Badge.Link*
references:
- uid: Unsplasharp.Models
  commentId: N:Unsplasharp.Models
  href: Unsplasharp.html
  name: Unsplasharp.Models
  nameWithType: Unsplasharp.Models
  fullName: Unsplasharp.Models
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Models.Badge.Title*
  commentId: Overload:Unsplasharp.Models.Badge.Title
  href: Unsplasharp.Models.Badge.html#Unsplasharp_Models_Badge_Title
  name: Title
  nameWithType: Badge.Title
  fullName: Unsplasharp.Models.Badge.Title
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Models.Badge.Primary*
  commentId: Overload:Unsplasharp.Models.Badge.Primary
  href: Unsplasharp.Models.Badge.html#Unsplasharp_Models_Badge_Primary
  name: Primary
  nameWithType: Badge.Primary
  fullName: Unsplasharp.Models.Badge.Primary
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: Unsplasharp.Models.Badge.Slug*
  commentId: Overload:Unsplasharp.Models.Badge.Slug
  href: Unsplasharp.Models.Badge.html#Unsplasharp_Models_Badge_Slug
  name: Slug
  nameWithType: Badge.Slug
  fullName: Unsplasharp.Models.Badge.Slug
- uid: Unsplasharp.Models.Badge.Link*
  commentId: Overload:Unsplasharp.Models.Badge.Link
  href: Unsplasharp.Models.Badge.html#Unsplasharp_Models_Badge_Link
  name: Link
  nameWithType: Badge.Link
  fullName: Unsplasharp.Models.Badge.Link
