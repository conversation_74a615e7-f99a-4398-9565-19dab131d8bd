{"Unsplasharp": "Unsplasharp.yml", "Unsplasharp.Exceptions": "Unsplasharp.Exceptions.yml", "Unsplasharp.Exceptions.ErrorContext": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.#ctor(System.String,System.String)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.ApplicationId": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.CorrelationId": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.ElapsedTime": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.FromRequest(System.Net.Http.HttpRequestMessage,System.String,System.String)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.FromResponse(System.Net.Http.HttpResponseMessage,System.String,System.String)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.GetProperty``1(System.String)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.Properties": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.RateLimitInfo": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.RequestHeaders": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.ResponseHeaders": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.RetryAttempts": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.Timestamp": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.ToSummary": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.WithElapsedTime(System.TimeSpan)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.WithProperty(System.String,System.Object)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.ErrorContext.WithRetryAttempts(System.Int32)": "Unsplasharp.Exceptions.ErrorContext.yml", "Unsplasharp.Exceptions.RateLimitInfo": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.#ctor(System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset})": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.FromHeaders(System.Net.Http.Headers.HttpResponseHeaders)": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.IsExceeded": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.Limit": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.Remaining": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.Reset": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.RateLimitInfo.TimeUntilReset": "Unsplasharp.Exceptions.RateLimitInfo.yml", "Unsplasharp.Exceptions.UnsplasharpAuthenticationException": "Unsplasharp.Exceptions.UnsplasharpAuthenticationException.yml", "Unsplasharp.Exceptions.UnsplasharpAuthenticationException.#ctor(System.String,System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpAuthenticationException.yml", "Unsplasharp.Exceptions.UnsplasharpException": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.#ctor(System.String)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.#ctor(System.String,System.Exception)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.#ctor(System.String,System.Exception,System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.#ctor(System.String,System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.Context": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.HttpMethod": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpException.RequestUrl": "Unsplasharp.Exceptions.UnsplasharpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Exception,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode": "Unsplasharp.Exceptions.UnsplasharpHttpException.yml", "Unsplasharp.Exceptions.UnsplasharpNetworkException": "Unsplasharp.Exceptions.UnsplasharpNetworkException.yml", "Unsplasharp.Exceptions.UnsplasharpNetworkException.#ctor(System.String,System.Exception,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpNetworkException.yml", "Unsplasharp.Exceptions.UnsplasharpNetworkException.IsRetryable": "Unsplasharp.Exceptions.UnsplasharpNetworkException.yml", "Unsplasharp.Exceptions.UnsplasharpNotFoundException": "Unsplasharp.Exceptions.UnsplasharpNotFoundException.yml", "Unsplasharp.Exceptions.UnsplasharpNotFoundException.#ctor(System.String,System.String,System.String,System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpNotFoundException.yml", "Unsplasharp.Exceptions.UnsplasharpNotFoundException.ResourceId": "Unsplasharp.Exceptions.UnsplasharpNotFoundException.yml", "Unsplasharp.Exceptions.UnsplasharpNotFoundException.ResourceType": "Unsplasharp.Exceptions.UnsplasharpNotFoundException.yml", "Unsplasharp.Exceptions.UnsplasharpParsingException": "Unsplasharp.Exceptions.UnsplasharpParsingException.yml", "Unsplasharp.Exceptions.UnsplasharpParsingException.#ctor(System.String,System.Exception,System.String,System.String,System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpParsingException.yml", "Unsplasharp.Exceptions.UnsplasharpParsingException.ExpectedType": "Unsplasharp.Exceptions.UnsplasharpParsingException.yml", "Unsplasharp.Exceptions.UnsplasharpParsingException.RawContent": "Unsplasharp.Exceptions.UnsplasharpParsingException.yml", "Unsplasharp.Exceptions.UnsplasharpRateLimitException": "Unsplasharp.Exceptions.UnsplasharpRateLimitException.yml", "Unsplasharp.Exceptions.UnsplasharpRateLimitException.#ctor(System.String,System.Nullable{System.Int32},System.Nullable{System.Int32},System.Nullable{System.DateTimeOffset},System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpRateLimitException.yml", "Unsplasharp.Exceptions.UnsplasharpRateLimitException.RateLimit": "Unsplasharp.Exceptions.UnsplasharpRateLimitException.yml", "Unsplasharp.Exceptions.UnsplasharpRateLimitException.RateLimitRemaining": "Unsplasharp.Exceptions.UnsplasharpRateLimitException.yml", "Unsplasharp.Exceptions.UnsplasharpRateLimitException.RateLimitReset": "Unsplasharp.Exceptions.UnsplasharpRateLimitException.yml", "Unsplasharp.Exceptions.UnsplasharpTimeoutException": "Unsplasharp.Exceptions.UnsplasharpTimeoutException.yml", "Unsplasharp.Exceptions.UnsplasharpTimeoutException.#ctor(System.String,System.Exception,System.Nullable{System.TimeSpan},System.String,System.String,Unsplasharp.Exceptions.ErrorContext)": "Unsplasharp.Exceptions.UnsplasharpTimeoutException.yml", "Unsplasharp.Exceptions.UnsplasharpTimeoutException.Timeout": "Unsplasharp.Exceptions.UnsplasharpTimeoutException.yml", "Unsplasharp.Extensions": "Unsplasharp.Extensions.yml", "Unsplasharp.Extensions.ServiceCollectionExtensions": "Unsplasharp.Extensions.ServiceCollectionExtensions.yml", "Unsplasharp.Extensions.ServiceCollectionExtensions.AddUnsplasharp(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.Action{Unsplasharp.Extensions.UnsplasharpOptions})": "Unsplasharp.Extensions.ServiceCollectionExtensions.yml", "Unsplasharp.Extensions.ServiceCollectionExtensions.AddUnsplasharp(Microsoft.Extensions.DependencyInjection.IServiceCollection,System.String,System.String,System.Action{System.Net.Http.HttpClient})": "Unsplasharp.Extensions.ServiceCollectionExtensions.yml", "Unsplasharp.Extensions.UnsplasharpOptions": "Unsplasharp.Extensions.UnsplasharpOptions.yml", "Unsplasharp.Extensions.UnsplasharpOptions.ApplicationId": "Unsplasharp.Extensions.UnsplasharpOptions.yml", "Unsplasharp.Extensions.UnsplasharpOptions.ConfigureHttpClient": "Unsplasharp.Extensions.UnsplasharpOptions.yml", "Unsplasharp.Extensions.UnsplasharpOptions.Secret": "Unsplasharp.Extensions.UnsplasharpOptions.yml", "Unsplasharp.Models": "Unsplasharp.Models.yml", "Unsplasharp.Models.Badge": "Unsplasharp.Models.Badge.yml", "Unsplasharp.Models.Badge.Link": "Unsplasharp.Models.Badge.yml", "Unsplasharp.Models.Badge.Primary": "Unsplasharp.Models.Badge.yml", "Unsplasharp.Models.Badge.Slug": "Unsplasharp.Models.Badge.yml", "Unsplasharp.Models.Badge.Title": "Unsplasharp.Models.Badge.yml", "Unsplasharp.Models.Category": "Unsplasharp.Models.Category.yml", "Unsplasharp.Models.Category.Id": "Unsplasharp.Models.Category.yml", "Unsplasharp.Models.Category.Links": "Unsplasharp.Models.Category.yml", "Unsplasharp.Models.Category.PhotoCount": "Unsplasharp.Models.Category.yml", "Unsplasharp.Models.Category.Title": "Unsplasharp.Models.Category.yml", "Unsplasharp.Models.CategoryLinks": "Unsplasharp.Models.CategoryLinks.yml", "Unsplasharp.Models.CategoryLinks.Photos": "Unsplasharp.Models.CategoryLinks.yml", "Unsplasharp.Models.CategoryLinks.Self": "Unsplasharp.Models.CategoryLinks.yml", "Unsplasharp.Models.Collection": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.CoverPhoto": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.Description": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.Id": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.IsCurated": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.IsFeatured": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.IsPrivate": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.Links": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.PropertyChanged": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.PublishedAt": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.ShareKey": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.Title": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.TotalPhotos": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.UpdatedAt": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.Collection.User": "Unsplasharp.Models.Collection.yml", "Unsplasharp.Models.CollectionLinks": "Unsplasharp.Models.CollectionLinks.yml", "Unsplasharp.Models.CollectionLinks.Html": "Unsplasharp.Models.CollectionLinks.yml", "Unsplasharp.Models.CollectionLinks.Photos": "Unsplasharp.Models.CollectionLinks.yml", "Unsplasharp.Models.CollectionLinks.Related": "Unsplasharp.Models.CollectionLinks.yml", "Unsplasharp.Models.CollectionLinks.Self": "Unsplasharp.Models.CollectionLinks.yml", "Unsplasharp.Models.Exif": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.Aperture": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.ExposureTime": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.FocalLength": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.Iso": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.Make": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Exif.Model": "Unsplasharp.Models.Exif.yml", "Unsplasharp.Models.Location": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Location.City": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Location.Country": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Location.Name": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Location.Position": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Location.Title": "Unsplasharp.Models.Location.yml", "Unsplasharp.Models.Photo": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.BlurHash": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Categories": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Color": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.CreatedAt": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.CurrentUserCollection": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Description": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Downloads": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Exif": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Height": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Id": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.IsLikedByUser": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Likes": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Links": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Location": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.PropertyChanged": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.UpdatedAt": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Urls": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.User": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.Photo.Width": "Unsplasharp.Models.Photo.yml", "Unsplasharp.Models.PhotoLinks": "Unsplasharp.Models.PhotoLinks.yml", "Unsplasharp.Models.PhotoLinks.Download": "Unsplasharp.Models.PhotoLinks.yml", "Unsplasharp.Models.PhotoLinks.DownloadLocation": "Unsplasharp.Models.PhotoLinks.yml", "Unsplasharp.Models.PhotoLinks.Html": "Unsplasharp.Models.PhotoLinks.yml", "Unsplasharp.Models.PhotoLinks.Self": "Unsplasharp.Models.PhotoLinks.yml", "Unsplasharp.Models.PhotoStats": "Unsplasharp.Models.PhotoStats.yml", "Unsplasharp.Models.PhotoStats.Downloads": "Unsplasharp.Models.PhotoStats.yml", "Unsplasharp.Models.PhotoStats.Id": "Unsplasharp.Models.PhotoStats.yml", "Unsplasharp.Models.PhotoStats.Likes": "Unsplasharp.Models.PhotoStats.yml", "Unsplasharp.Models.PhotoStats.Views": "Unsplasharp.Models.PhotoStats.yml", "Unsplasharp.Models.Position": "Unsplasharp.Models.Position.yml", "Unsplasharp.Models.Position.Latitude": "Unsplasharp.Models.Position.yml", "Unsplasharp.Models.Position.Longitude": "Unsplasharp.Models.Position.yml", "Unsplasharp.Models.ProfileImage": "Unsplasharp.Models.ProfileImage.yml", "Unsplasharp.Models.ProfileImage.Large": "Unsplasharp.Models.ProfileImage.yml", "Unsplasharp.Models.ProfileImage.Medium": "Unsplasharp.Models.ProfileImage.yml", "Unsplasharp.Models.ProfileImage.Small": "Unsplasharp.Models.ProfileImage.yml", "Unsplasharp.Models.StatsData": "Unsplasharp.Models.StatsData.yml", "Unsplasharp.Models.StatsData.Historical": "Unsplasharp.Models.StatsData.yml", "Unsplasharp.Models.StatsData.Total": "Unsplasharp.Models.StatsData.yml", "Unsplasharp.Models.StatsHistorical": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsHistorical.Average": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsHistorical.Change": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsHistorical.Quantity": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsHistorical.Resolution": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsHistorical.Values": "Unsplasharp.Models.StatsHistorical.yml", "Unsplasharp.Models.StatsValue": "Unsplasharp.Models.StatsValue.yml", "Unsplasharp.Models.StatsValue.Date": "Unsplasharp.Models.StatsValue.yml", "Unsplasharp.Models.StatsValue.Value": "Unsplasharp.Models.StatsValue.yml", "Unsplasharp.Models.UnplashMonthlyStats": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.Downloads": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.Likes": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewApplications": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewDevelopers": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewPhotographers": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewPhotos": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewPixels": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.NewRequests": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashMonthlyStats.Views": "Unsplasharp.Models.UnplashMonthlyStats.yml", "Unsplasharp.Models.UnplashTotalStats": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Applications": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Developers": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Downloads": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.DownloadsPerSecond": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Likes": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Photographers": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Photos": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Pixels": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Requests": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.Views": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.UnplashTotalStats.ViewsPerSecond": "Unsplasharp.Models.UnplashTotalStats.yml", "Unsplasharp.Models.Urls": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Custom": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Full": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Raw": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Regular": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Small": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.Urls.Thumbnail": "Unsplasharp.Models.Urls.yml", "Unsplasharp.Models.User": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Badge": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Bio": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Downloads": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.FirstName": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.FollowedByUser": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.FollowersCount": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.FollowingCount": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Id": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.LastName": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Links": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Location": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Name": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.PortfolioUrl": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.ProfileImage": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.PropertyChanged": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.TotalCollections": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.TotalLikes": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.TotalPhotos": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.TwitterUsername": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.UpdatedAt": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.User.Username": "Unsplasharp.Models.User.yml", "Unsplasharp.Models.UserLinks": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserLinks.Html": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserLinks.Likes": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserLinks.Photos": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserLinks.Portfolio": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserLinks.Self": "Unsplasharp.Models.UserLinks.yml", "Unsplasharp.Models.UserStats": "Unsplasharp.Models.UserStats.yml", "Unsplasharp.Models.UserStats.Downloads": "Unsplasharp.Models.UserStats.yml", "Unsplasharp.Models.UserStats.Likes": "Unsplasharp.Models.UserStats.yml", "Unsplasharp.Models.UserStats.Username": "Unsplasharp.Models.UserStats.yml", "Unsplasharp.Models.UserStats.Views": "Unsplasharp.Models.UserStats.yml", "Unsplasharp.UnsplasharpClient": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.#ctor(System.String,System.String,Microsoft.Extensions.Logging.ILogger{Unsplasharp.UnsplasharpClient},System.Net.Http.IHttpClientFactory)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ApplicationId": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchCollectionsList(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchCollectionsList(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchPhoto(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchPhoto(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchPhotosList(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchPhotosList(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchSearcCollectionsList(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchSearcCollectionsList(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchSearcUsersList(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchSearchPhotosList(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.FetchSearchPhotosList(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetCollection(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetCollection(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetCollectionPhotos(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetCollectionPhotos(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetMonthlyStats": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhoto(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhoto(System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhoto(System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhoto(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhotoAsync(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhotoAsync(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhotoDownloadLink(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetPhotoStats(System.String,Unsplasharp.UnsplasharpClient.Resolution,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.Boolean,System.String,System.String,System.Int32,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.Int32,System.String,System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.String[])": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.String[],System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(Unsplasharp.UnsplasharpClient.Orientation,System.Boolean,System.String,System.String,System.Int32,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhoto(Unsplasharp.UnsplasharpClient.Orientation,System.String,System.String,System.Int32,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhotoAsync": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetRandomPhotoAsync(System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetTotalStats": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetTotalStats(System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetUser(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetUser(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetUserPorfolioLink(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.GetUserStats(System.String,Unsplasharp.UnsplasharpClient.Resolution,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastCollectionsSearchQuery": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastCollectionsSearchTotalPages": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastCollectionsSearchTotalResults": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastPhotosSearchQuery": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastPhotosSearchTotalPages": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastPhotosSearchTotalResults": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastUsersSearchQuery": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastUsersSearchTotalPages": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.LastUsersSearchTotalResults": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListCollections(System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListCollections(System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListFeaturedCollections(System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListFeaturedCollections(System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListPhotos(System.Int32,System.Int32,Unsplasharp.UnsplasharpClient.OrderBy)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListPhotos(System.Int32,System.Int32,Unsplasharp.UnsplasharpClient.OrderBy,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListRelatedCollections(System.String)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListRelatedCollections(System.String,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListUserCollections(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListUserLikedPhotos(System.String,System.Int32,System.Int32,Unsplasharp.UnsplasharpClient.OrderBy)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.ListUserPhotos(System.String,System.Int32,System.Int32,Unsplasharp.UnsplasharpClient.OrderBy,System.Boolean,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.MaxRateLimit": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.OrderBy": "Unsplasharp.UnsplasharpClient.OrderBy.yml", "Unsplasharp.UnsplasharpClient.OrderBy.Latest": "Unsplasharp.UnsplasharpClient.OrderBy.yml", "Unsplasharp.UnsplasharpClient.OrderBy.Oldest": "Unsplasharp.UnsplasharpClient.OrderBy.yml", "Unsplasharp.UnsplasharpClient.OrderBy.Popular": "Unsplasharp.UnsplasharpClient.OrderBy.yml", "Unsplasharp.UnsplasharpClient.Orientation": "Unsplasharp.UnsplasharpClient.Orientation.yml", "Unsplasharp.UnsplasharpClient.Orientation.Landscape": "Unsplasharp.UnsplasharpClient.Orientation.yml", "Unsplasharp.UnsplasharpClient.Orientation.Portrait": "Unsplasharp.UnsplasharpClient.Orientation.yml", "Unsplasharp.UnsplasharpClient.Orientation.Squarish": "Unsplasharp.UnsplasharpClient.Orientation.yml", "Unsplasharp.UnsplasharpClient.RateLimitRemaining": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.Resolution": "Unsplasharp.UnsplasharpClient.Resolution.yml", "Unsplasharp.UnsplasharpClient.Resolution.Days": "Unsplasharp.UnsplasharpClient.Resolution.yml", "Unsplasharp.UnsplasharpClient.SearchCollections(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchCollections(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.String,System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.String[],System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchPhotos(System.String,System.String[],System.Int32,System.Int32,System.Threading.CancellationToken)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.SearchUsers(System.String,System.Int32,System.Int32)": "Unsplasharp.UnsplasharpClient.yml", "Unsplasharp.UnsplasharpClient.Secret": "Unsplasharp.UnsplasharpClient.yml"}