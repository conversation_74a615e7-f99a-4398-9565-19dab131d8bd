### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException
  commentId: T:Unsplasharp.Exceptions.UnsplasharpHttpException
  id: UnsplasharpHttpException
  parent: Unsplasharp.Exceptions
  children:
  - Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Exception,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  - Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  - Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
  - Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
  - Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
  langs:
  - csharp
  - vb
  name: UnsplasharpHttpException
  nameWithType: UnsplasharpHttpException
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: UnsplasharpHttpException
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 78
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Exception thrown when an HTTP request fails
  example: []
  syntax:
    content: 'public class UnsplasharpHttpException : UnsplasharpException, ISerializable'
    content.vb: Public Class UnsplasharpHttpException Inherits UnsplasharpException Implements ISerializable
  inheritance:
  - System.Object
  - System.Exception
  - Unsplasharp.Exceptions.UnsplasharpException
  derivedClasses:
  - Unsplasharp.Exceptions.UnsplasharpAuthenticationException
  - Unsplasharp.Exceptions.UnsplasharpNotFoundException
  - Unsplasharp.Exceptions.UnsplasharpRateLimitException
  implements:
  - System.Runtime.Serialization.ISerializable
  inheritedMembers:
  - Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)
  - Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)
  - Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)
  - Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)
  - Unsplasharp.Exceptions.UnsplasharpException.RequestUrl
  - Unsplasharp.Exceptions.UnsplasharpException.HttpMethod
  - Unsplasharp.Exceptions.UnsplasharpException.Context
  - System.Exception.GetBaseException
  - System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
  - System.Exception.GetType
  - System.Exception.ToString
  - System.Exception.Data
  - System.Exception.HelpLink
  - System.Exception.HResult
  - System.Exception.InnerException
  - System.Exception.Message
  - System.Exception.Source
  - System.Exception.StackTrace
  - System.Exception.TargetSite
  - System.Exception.SerializeObjectState
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
  commentId: P:Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
  id: StatusCode
  parent: Unsplasharp.Exceptions.UnsplasharpHttpException
  langs:
  - csharp
  - vb
  name: StatusCode
  nameWithType: UnsplasharpHttpException.StatusCode
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: StatusCode
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 83
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: The HTTP status code returned by the API
  example: []
  syntax:
    content: public HttpStatusCode? StatusCode { get; }
    parameters: []
    return:
      type: System.Nullable{System.Net.HttpStatusCode}
    content.vb: Public ReadOnly Property StatusCode As HttpStatusCode?
  overload: Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode*
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
  commentId: P:Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
  id: ResponseContent
  parent: Unsplasharp.Exceptions.UnsplasharpHttpException
  langs:
  - csharp
  - vb
  name: ResponseContent
  nameWithType: UnsplasharpHttpException.ResponseContent
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: ResponseContent
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 88
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: The response content from the API
  example: []
  syntax:
    content: public string? ResponseContent { get; }
    parameters: []
    return:
      type: System.String
    content.vb: Public ReadOnly Property ResponseContent As String
  overload: Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent*
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
  commentId: P:Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
  id: IsRetryable
  parent: Unsplasharp.Exceptions.UnsplasharpHttpException
  langs:
  - csharp
  - vb
  name: IsRetryable
  nameWithType: UnsplasharpHttpException.IsRetryable
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: IsRetryable
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 93
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Indicates whether this error is retryable
  example: []
  syntax:
    content: public bool IsRetryable { get; }
    parameters: []
    return:
      type: System.Boolean
    content.vb: Public ReadOnly Property IsRetryable As Boolean
  overload: Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable*
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  id: '#ctor(System.String,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)'
  parent: Unsplasharp.Exceptions.UnsplasharpHttpException
  langs:
  - csharp
  - vb
  name: UnsplasharpHttpException(string, HttpStatusCode?, string?, string?, string?, bool, ErrorContext?)
  nameWithType: UnsplasharpHttpException.UnsplasharpHttpException(string, HttpStatusCode?, string?, string?, string?, bool, ErrorContext?)
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.UnsplasharpHttpException(string, System.Net.HttpStatusCode?, string?, string?, string?, bool, Unsplasharp.Exceptions.ErrorContext?)
  type: Constructor
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: .ctor
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 105
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Initializes a new instance of the UnsplasharpHttpException class
  example: []
  syntax:
    content: public UnsplasharpHttpException(string message, HttpStatusCode? statusCode, string? responseContent, string? requestUrl, string? httpMethod, bool isRetryable = false, ErrorContext? context = null)
    parameters:
    - id: message
      type: System.String
      description: The error message
    - id: statusCode
      type: System.Nullable{System.Net.HttpStatusCode}
      description: The HTTP status code
    - id: responseContent
      type: System.String
      description: The response content
    - id: requestUrl
      type: System.String
      description: The request URL
    - id: httpMethod
      type: System.String
      description: The HTTP method
    - id: isRetryable
      type: System.Boolean
      description: Whether this error is retryable
    - id: context
      type: Unsplasharp.Exceptions.ErrorContext
      description: Additional error context
    content.vb: Public Sub New(message As String, statusCode As HttpStatusCode?, responseContent As String, requestUrl As String, httpMethod As String, isRetryable As Boolean = False, context As ErrorContext = Nothing)
  overload: Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor*
  nameWithType.vb: UnsplasharpHttpException.New(String, HttpStatusCode?, String, String, String, Boolean, ErrorContext)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpHttpException.New(String, System.Net.HttpStatusCode?, String, String, String, Boolean, Unsplasharp.Exceptions.ErrorContext)
  name.vb: New(String, HttpStatusCode?, String, String, String, Boolean, ErrorContext)
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Exception,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor(System.String,System.Exception,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)
  id: '#ctor(System.String,System.Exception,System.Nullable{System.Net.HttpStatusCode},System.String,System.String,System.String,System.Boolean,Unsplasharp.Exceptions.ErrorContext)'
  parent: Unsplasharp.Exceptions.UnsplasharpHttpException
  langs:
  - csharp
  - vb
  name: UnsplasharpHttpException(string, Exception, HttpStatusCode?, string?, string?, string?, bool, ErrorContext?)
  nameWithType: UnsplasharpHttpException.UnsplasharpHttpException(string, Exception, HttpStatusCode?, string?, string?, string?, bool, ErrorContext?)
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.UnsplasharpHttpException(string, System.Exception, System.Net.HttpStatusCode?, string?, string?, string?, bool, Unsplasharp.Exceptions.ErrorContext?)
  type: Constructor
  source:
    remote:
      path: src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: .ctor
    path: ../src/unsplasharp/Source/Exceptions/UnsplasharpException.cs
    startLine: 125
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Exceptions
  summary: Initializes a new instance of the UnsplasharpHttpException class with inner exception
  example: []
  syntax:
    content: public UnsplasharpHttpException(string message, Exception innerException, HttpStatusCode? statusCode, string? responseContent, string? requestUrl, string? httpMethod, bool isRetryable = false, ErrorContext? context = null)
    parameters:
    - id: message
      type: System.String
      description: The error message
    - id: innerException
      type: System.Exception
      description: The inner exception
    - id: statusCode
      type: System.Nullable{System.Net.HttpStatusCode}
      description: The HTTP status code
    - id: responseContent
      type: System.String
      description: The response content
    - id: requestUrl
      type: System.String
      description: The request URL
    - id: httpMethod
      type: System.String
      description: The HTTP method
    - id: isRetryable
      type: System.Boolean
      description: Whether this error is retryable
    - id: context
      type: Unsplasharp.Exceptions.ErrorContext
      description: Additional error context
    content.vb: Public Sub New(message As String, innerException As Exception, statusCode As HttpStatusCode?, responseContent As String, requestUrl As String, httpMethod As String, isRetryable As Boolean = False, context As ErrorContext = Nothing)
  overload: Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor*
  nameWithType.vb: UnsplasharpHttpException.New(String, Exception, HttpStatusCode?, String, String, String, Boolean, ErrorContext)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpHttpException.New(String, System.Exception, System.Net.HttpStatusCode?, String, String, String, Boolean, Unsplasharp.Exceptions.ErrorContext)
  name.vb: New(String, Exception, HttpStatusCode?, String, String, String, Boolean, ErrorContext)
references:
- uid: Unsplasharp.Exceptions
  commentId: N:Unsplasharp.Exceptions
  href: Unsplasharp.html
  name: Unsplasharp.Exceptions
  nameWithType: Unsplasharp.Exceptions
  fullName: Unsplasharp.Exceptions
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Exceptions
    name: Exceptions
    href: Unsplasharp.Exceptions.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Exceptions
    name: Exceptions
    href: Unsplasharp.Exceptions.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Exception
  commentId: T:System.Exception
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception
  name: Exception
  nameWithType: Exception
  fullName: System.Exception
- uid: Unsplasharp.Exceptions.UnsplasharpException
  commentId: T:Unsplasharp.Exceptions.UnsplasharpException
  parent: Unsplasharp.Exceptions
  href: Unsplasharp.Exceptions.UnsplasharpException.html
  name: UnsplasharpException
  nameWithType: UnsplasharpException
  fullName: Unsplasharp.Exceptions.UnsplasharpException
- uid: System.Runtime.Serialization.ISerializable
  commentId: T:System.Runtime.Serialization.ISerializable
  parent: System.Runtime.Serialization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization.iserializable
  name: ISerializable
  nameWithType: ISerializable
  fullName: System.Runtime.Serialization.ISerializable
- uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)
  parent: Unsplasharp.Exceptions.UnsplasharpException
  isExternal: true
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpResponse_System_Net_Http_HttpResponseMessage_System_String_System_String_System_String_
  name: FromHttpResponse(HttpResponseMessage, string, string, string)
  nameWithType: UnsplasharpException.FromHttpResponse(HttpResponseMessage, string, string, string)
  fullName: Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage, string, string, string)
  nameWithType.vb: UnsplasharpException.FromHttpResponse(HttpResponseMessage, String, String, String)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage, String, String, String)
  name.vb: FromHttpResponse(HttpResponseMessage, String, String, String)
  spec.csharp:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)
    name: FromHttpResponse
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpResponse_System_Net_Http_HttpResponseMessage_System_String_System_String_System_String_
  - name: (
  - uid: System.Net.Http.HttpResponseMessage
    name: HttpResponseMessage
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httpresponsemessage
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpResponse(System.Net.Http.HttpResponseMessage,System.String,System.String,System.String)
    name: FromHttpResponse
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpResponse_System_Net_Http_HttpResponseMessage_System_String_System_String_System_String_
  - name: (
  - uid: System.Net.Http.HttpResponseMessage
    name: HttpResponseMessage
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httpresponsemessage
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)
  parent: Unsplasharp.Exceptions.UnsplasharpException
  isExternal: true
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpRequestException_System_Net_Http_HttpRequestException_System_String_System_String_System_String_System_String_
  name: FromHttpRequestException(HttpRequestException, string, string, string, string)
  nameWithType: UnsplasharpException.FromHttpRequestException(HttpRequestException, string, string, string, string)
  fullName: Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException, string, string, string, string)
  nameWithType.vb: UnsplasharpException.FromHttpRequestException(HttpRequestException, String, String, String, String)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException, String, String, String, String)
  name.vb: FromHttpRequestException(HttpRequestException, String, String, String, String)
  spec.csharp:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)
    name: FromHttpRequestException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpRequestException_System_Net_Http_HttpRequestException_System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Net.Http.HttpRequestException
    name: HttpRequestException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httprequestexception
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromHttpRequestException(System.Net.Http.HttpRequestException,System.String,System.String,System.String,System.String)
    name: FromHttpRequestException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromHttpRequestException_System_Net_Http_HttpRequestException_System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Net.Http.HttpRequestException
    name: HttpRequestException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.http.httprequestexception
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)
  parent: Unsplasharp.Exceptions.UnsplasharpException
  isExternal: true
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromTaskCanceledException_System_Threading_Tasks_TaskCanceledException_System_Nullable_System_TimeSpan__System_String_System_String_System_String_System_String_
  name: FromTaskCanceledException(TaskCanceledException, TimeSpan?, string, string, string, string)
  nameWithType: UnsplasharpException.FromTaskCanceledException(TaskCanceledException, TimeSpan?, string, string, string, string)
  fullName: Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException, System.TimeSpan?, string, string, string, string)
  nameWithType.vb: UnsplasharpException.FromTaskCanceledException(TaskCanceledException, TimeSpan?, String, String, String, String)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException, System.TimeSpan?, String, String, String, String)
  name.vb: FromTaskCanceledException(TaskCanceledException, TimeSpan?, String, String, String, String)
  spec.csharp:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)
    name: FromTaskCanceledException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromTaskCanceledException_System_Threading_Tasks_TaskCanceledException_System_Nullable_System_TimeSpan__System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Threading.Tasks.TaskCanceledException
    name: TaskCanceledException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcanceledexception
  - name: ','
  - name: " "
  - uid: System.TimeSpan
    name: TimeSpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.timespan
  - name: '?'
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromTaskCanceledException(System.Threading.Tasks.TaskCanceledException,System.Nullable{System.TimeSpan},System.String,System.String,System.String,System.String)
    name: FromTaskCanceledException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromTaskCanceledException_System_Threading_Tasks_TaskCanceledException_System_Nullable_System_TimeSpan__System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Threading.Tasks.TaskCanceledException
    name: TaskCanceledException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.threading.tasks.taskcanceledexception
  - name: ','
  - name: " "
  - uid: System.TimeSpan
    name: TimeSpan
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.timespan
  - name: '?'
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)
  commentId: M:Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)
  parent: Unsplasharp.Exceptions.UnsplasharpException
  isExternal: true
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromJsonException_System_Text_Json_JsonException_System_String_System_String_System_String_System_String_System_String_System_String_
  name: FromJsonException(JsonException, string, string, string, string, string, string)
  nameWithType: UnsplasharpException.FromJsonException(JsonException, string, string, string, string, string, string)
  fullName: Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException, string, string, string, string, string, string)
  nameWithType.vb: UnsplasharpException.FromJsonException(JsonException, String, String, String, String, String, String)
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException, String, String, String, String, String, String)
  name.vb: FromJsonException(JsonException, String, String, String, String, String, String)
  spec.csharp:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)
    name: FromJsonException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromJsonException_System_Text_Json_JsonException_System_String_System_String_System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Text.Json.JsonException
    name: JsonException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.text.json.jsonexception
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: string
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
  spec.vb:
  - uid: Unsplasharp.Exceptions.UnsplasharpException.FromJsonException(System.Text.Json.JsonException,System.String,System.String,System.String,System.String,System.String,System.String)
    name: FromJsonException
    href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_FromJsonException_System_Text_Json_JsonException_System_String_System_String_System_String_System_String_System_String_System_String_
  - name: (
  - uid: System.Text.Json.JsonException
    name: JsonException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.text.json.jsonexception
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: ','
  - name: " "
  - uid: System.String
    name: String
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.string
  - name: )
- uid: Unsplasharp.Exceptions.UnsplasharpException.RequestUrl
  commentId: P:Unsplasharp.Exceptions.UnsplasharpException.RequestUrl
  parent: Unsplasharp.Exceptions.UnsplasharpException
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_RequestUrl
  name: RequestUrl
  nameWithType: UnsplasharpException.RequestUrl
  fullName: Unsplasharp.Exceptions.UnsplasharpException.RequestUrl
- uid: Unsplasharp.Exceptions.UnsplasharpException.HttpMethod
  commentId: P:Unsplasharp.Exceptions.UnsplasharpException.HttpMethod
  parent: Unsplasharp.Exceptions.UnsplasharpException
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_HttpMethod
  name: HttpMethod
  nameWithType: UnsplasharpException.HttpMethod
  fullName: Unsplasharp.Exceptions.UnsplasharpException.HttpMethod
- uid: Unsplasharp.Exceptions.UnsplasharpException.Context
  commentId: P:Unsplasharp.Exceptions.UnsplasharpException.Context
  parent: Unsplasharp.Exceptions.UnsplasharpException
  href: Unsplasharp.Exceptions.UnsplasharpException.html#Unsplasharp_Exceptions_UnsplasharpException_Context
  name: Context
  nameWithType: UnsplasharpException.Context
  fullName: Unsplasharp.Exceptions.UnsplasharpException.Context
- uid: System.Exception.GetBaseException
  commentId: M:System.Exception.GetBaseException
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.getbaseexception
  name: GetBaseException()
  nameWithType: Exception.GetBaseException()
  fullName: System.Exception.GetBaseException()
  spec.csharp:
  - uid: System.Exception.GetBaseException
    name: GetBaseException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.getbaseexception
  - name: (
  - name: )
  spec.vb:
  - uid: System.Exception.GetBaseException
    name: GetBaseException
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.getbaseexception
  - name: (
  - name: )
- uid: System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
  commentId: M:System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.getobjectdata
  name: GetObjectData(SerializationInfo, StreamingContext)
  nameWithType: Exception.GetObjectData(SerializationInfo, StreamingContext)
  fullName: System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo, System.Runtime.Serialization.StreamingContext)
  spec.csharp:
  - uid: System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
    name: GetObjectData
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.getobjectdata
  - name: (
  - uid: System.Runtime.Serialization.SerializationInfo
    name: SerializationInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization.serializationinfo
  - name: ','
  - name: " "
  - uid: System.Runtime.Serialization.StreamingContext
    name: StreamingContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization.streamingcontext
  - name: )
  spec.vb:
  - uid: System.Exception.GetObjectData(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)
    name: GetObjectData
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.getobjectdata
  - name: (
  - uid: System.Runtime.Serialization.SerializationInfo
    name: SerializationInfo
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization.serializationinfo
  - name: ','
  - name: " "
  - uid: System.Runtime.Serialization.StreamingContext
    name: StreamingContext
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization.streamingcontext
  - name: )
- uid: System.Exception.GetType
  commentId: M:System.Exception.GetType
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.gettype
  name: GetType()
  nameWithType: Exception.GetType()
  fullName: System.Exception.GetType()
  spec.csharp:
  - uid: System.Exception.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Exception.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.gettype
  - name: (
  - name: )
- uid: System.Exception.ToString
  commentId: M:System.Exception.ToString
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.tostring
  name: ToString()
  nameWithType: Exception.ToString()
  fullName: System.Exception.ToString()
  spec.csharp:
  - uid: System.Exception.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Exception.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.exception.tostring
  - name: (
  - name: )
- uid: System.Exception.Data
  commentId: P:System.Exception.Data
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.data
  name: Data
  nameWithType: Exception.Data
  fullName: System.Exception.Data
- uid: System.Exception.HelpLink
  commentId: P:System.Exception.HelpLink
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.helplink
  name: HelpLink
  nameWithType: Exception.HelpLink
  fullName: System.Exception.HelpLink
- uid: System.Exception.HResult
  commentId: P:System.Exception.HResult
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.hresult
  name: HResult
  nameWithType: Exception.HResult
  fullName: System.Exception.HResult
- uid: System.Exception.InnerException
  commentId: P:System.Exception.InnerException
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.innerexception
  name: InnerException
  nameWithType: Exception.InnerException
  fullName: System.Exception.InnerException
- uid: System.Exception.Message
  commentId: P:System.Exception.Message
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.message
  name: Message
  nameWithType: Exception.Message
  fullName: System.Exception.Message
- uid: System.Exception.Source
  commentId: P:System.Exception.Source
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.source
  name: Source
  nameWithType: Exception.Source
  fullName: System.Exception.Source
- uid: System.Exception.StackTrace
  commentId: P:System.Exception.StackTrace
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.stacktrace
  name: StackTrace
  nameWithType: Exception.StackTrace
  fullName: System.Exception.StackTrace
- uid: System.Exception.TargetSite
  commentId: P:System.Exception.TargetSite
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.targetsite
  name: TargetSite
  nameWithType: Exception.TargetSite
  fullName: System.Exception.TargetSite
- uid: System.Exception.SerializeObjectState
  commentId: E:System.Exception.SerializeObjectState
  parent: System.Exception
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.exception.serializeobjectstate
  name: SerializeObjectState
  nameWithType: Exception.SerializeObjectState
  fullName: System.Exception.SerializeObjectState
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: System.Runtime.Serialization
  commentId: N:System.Runtime.Serialization
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System.Runtime.Serialization
  nameWithType: System.Runtime.Serialization
  fullName: System.Runtime.Serialization
  spec.csharp:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Runtime
    name: Runtime
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime
  - name: .
  - uid: System.Runtime.Serialization
    name: Serialization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization
  spec.vb:
  - uid: System
    name: System
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system
  - name: .
  - uid: System.Runtime
    name: Runtime
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime
  - name: .
  - uid: System.Runtime.Serialization
    name: Serialization
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.runtime.serialization
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode*
  commentId: Overload:Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
  href: Unsplasharp.Exceptions.UnsplasharpHttpException.html#Unsplasharp_Exceptions_UnsplasharpHttpException_StatusCode
  name: StatusCode
  nameWithType: UnsplasharpHttpException.StatusCode
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.StatusCode
- uid: System.Nullable{System.Net.HttpStatusCode}
  commentId: T:System.Nullable{System.Net.HttpStatusCode}
  parent: System
  definition: System.Nullable`1
  href: https://learn.microsoft.com/dotnet/api/system.net.httpstatuscode
  name: HttpStatusCode?
  nameWithType: HttpStatusCode?
  fullName: System.Net.HttpStatusCode?
  spec.csharp:
  - uid: System.Net.HttpStatusCode
    name: HttpStatusCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.httpstatuscode
  - name: '?'
  spec.vb:
  - uid: System.Net.HttpStatusCode
    name: HttpStatusCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.net.httpstatuscode
  - name: '?'
- uid: System.Nullable`1
  commentId: T:System.Nullable`1
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  name: Nullable<T>
  nameWithType: Nullable<T>
  fullName: System.Nullable<T>
  nameWithType.vb: Nullable(Of T)
  fullName.vb: System.Nullable(Of T)
  name.vb: Nullable(Of T)
  spec.csharp:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: <
  - name: T
  - name: '>'
  spec.vb:
  - uid: System.Nullable`1
    name: Nullable
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.nullable-1
  - name: (
  - name: Of
  - name: " "
  - name: T
  - name: )
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent*
  commentId: Overload:Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
  href: Unsplasharp.Exceptions.UnsplasharpHttpException.html#Unsplasharp_Exceptions_UnsplasharpHttpException_ResponseContent
  name: ResponseContent
  nameWithType: UnsplasharpHttpException.ResponseContent
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.ResponseContent
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable*
  commentId: Overload:Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
  href: Unsplasharp.Exceptions.UnsplasharpHttpException.html#Unsplasharp_Exceptions_UnsplasharpHttpException_IsRetryable
  name: IsRetryable
  nameWithType: UnsplasharpHttpException.IsRetryable
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.IsRetryable
- uid: System.Boolean
  commentId: T:System.Boolean
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.boolean
  name: bool
  nameWithType: bool
  fullName: bool
  nameWithType.vb: Boolean
  fullName.vb: Boolean
  name.vb: Boolean
- uid: Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor*
  commentId: Overload:Unsplasharp.Exceptions.UnsplasharpHttpException.#ctor
  href: Unsplasharp.Exceptions.UnsplasharpHttpException.html#Unsplasharp_Exceptions_UnsplasharpHttpException__ctor_System_String_System_Nullable_System_Net_HttpStatusCode__System_String_System_String_System_String_System_Boolean_Unsplasharp_Exceptions_ErrorContext_
  name: UnsplasharpHttpException
  nameWithType: UnsplasharpHttpException.UnsplasharpHttpException
  fullName: Unsplasharp.Exceptions.UnsplasharpHttpException.UnsplasharpHttpException
  nameWithType.vb: UnsplasharpHttpException.New
  fullName.vb: Unsplasharp.Exceptions.UnsplasharpHttpException.New
  name.vb: New
- uid: Unsplasharp.Exceptions.ErrorContext
  commentId: T:Unsplasharp.Exceptions.ErrorContext
  parent: Unsplasharp.Exceptions
  href: Unsplasharp.Exceptions.ErrorContext.html
  name: ErrorContext
  nameWithType: ErrorContext
  fullName: Unsplasharp.Exceptions.ErrorContext
