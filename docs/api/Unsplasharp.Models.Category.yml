### YamlMime:ManagedReference
items:
- uid: Unsplasharp.Models.Category
  commentId: T:Unsplasharp.Models.Category
  id: Category
  parent: Unsplasharp.Models
  children:
  - Unsplasharp.Models.Category.Id
  - Unsplasharp.Models.Category.Links
  - Unsplasharp.Models.Category.PhotoCount
  - Unsplasharp.Models.Category.Title
  langs:
  - csharp
  - vb
  name: Category
  nameWithType: Category
  fullName: Unsplasharp.Models.Category
  type: Class
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Category
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 386
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Photo's category.
  example: []
  syntax:
    content: public class Category
    content.vb: Public Class Category
  inheritance:
  - System.Object
  inheritedMembers:
  - System.Object.Equals(System.Object)
  - System.Object.Equals(System.Object,System.Object)
  - System.Object.GetHashCode
  - System.Object.GetType
  - System.Object.MemberwiseClone
  - System.Object.ReferenceEquals(System.Object,System.Object)
  - System.Object.ToString
- uid: Unsplasharp.Models.Category.Id
  commentId: P:Unsplasharp.Models.Category.Id
  id: Id
  parent: Unsplasharp.Models.Category
  langs:
  - csharp
  - vb
  name: Id
  nameWithType: Category.Id
  fullName: Unsplasharp.Models.Category.Id
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Id
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 390
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Category's identifier.
  example: []
  syntax:
    content: public string Id { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Id As String
  overload: Unsplasharp.Models.Category.Id*
- uid: Unsplasharp.Models.Category.Title
  commentId: P:Unsplasharp.Models.Category.Title
  id: Title
  parent: Unsplasharp.Models.Category
  langs:
  - csharp
  - vb
  name: Title
  nameWithType: Category.Title
  fullName: Unsplasharp.Models.Category.Title
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Title
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 395
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Category's title.
  example: []
  syntax:
    content: public string Title { get; set; }
    parameters: []
    return:
      type: System.String
    content.vb: Public Property Title As String
  overload: Unsplasharp.Models.Category.Title*
- uid: Unsplasharp.Models.Category.PhotoCount
  commentId: P:Unsplasharp.Models.Category.PhotoCount
  id: PhotoCount
  parent: Unsplasharp.Models.Category
  langs:
  - csharp
  - vb
  name: PhotoCount
  nameWithType: Category.PhotoCount
  fullName: Unsplasharp.Models.Category.PhotoCount
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: PhotoCount
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 400
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Number of photos in this category.
  example: []
  syntax:
    content: public int PhotoCount { get; set; }
    parameters: []
    return:
      type: System.Int32
    content.vb: Public Property PhotoCount As Integer
  overload: Unsplasharp.Models.Category.PhotoCount*
- uid: Unsplasharp.Models.Category.Links
  commentId: P:Unsplasharp.Models.Category.Links
  id: Links
  parent: Unsplasharp.Models.Category
  langs:
  - csharp
  - vb
  name: Links
  nameWithType: Category.Links
  fullName: Unsplasharp.Models.Category.Links
  type: Property
  source:
    remote:
      path: src/unsplasharp/Source/Models/Photo.cs
      branch: master
      repo: https://github.com/rootasjey/unsplasharp.git
    id: Links
    path: ../src/unsplasharp/Source/Models/Photo.cs
    startLine: 405
  assemblies:
  - Unsplasharp
  namespace: Unsplasharp.Models
  summary: Category's links.
  example: []
  syntax:
    content: public CategoryLinks Links { get; set; }
    parameters: []
    return:
      type: Unsplasharp.Models.CategoryLinks
    content.vb: Public Property Links As CategoryLinks
  overload: Unsplasharp.Models.Category.Links*
references:
- uid: Unsplasharp.Models
  commentId: N:Unsplasharp.Models
  href: Unsplasharp.html
  name: Unsplasharp.Models
  nameWithType: Unsplasharp.Models
  fullName: Unsplasharp.Models
  spec.csharp:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
  spec.vb:
  - uid: Unsplasharp
    name: Unsplasharp
    href: Unsplasharp.html
  - name: .
  - uid: Unsplasharp.Models
    name: Models
    href: Unsplasharp.Models.html
- uid: System.Object
  commentId: T:System.Object
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object
  name: object
  nameWithType: object
  fullName: object
  nameWithType.vb: Object
  fullName.vb: Object
  name.vb: Object
- uid: System.Object.Equals(System.Object)
  commentId: M:System.Object.Equals(System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  name: Equals(object)
  nameWithType: object.Equals(object)
  fullName: object.Equals(object)
  nameWithType.vb: Object.Equals(Object)
  fullName.vb: Object.Equals(Object)
  name.vb: Equals(Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.Equals(System.Object,System.Object)
  commentId: M:System.Object.Equals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  name: Equals(object, object)
  nameWithType: object.Equals(object, object)
  fullName: object.Equals(object, object)
  nameWithType.vb: Object.Equals(Object, Object)
  fullName.vb: Object.Equals(Object, Object)
  name.vb: Equals(Object, Object)
  spec.csharp:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.Equals(System.Object,System.Object)
    name: Equals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.equals#system-object-equals(system-object-system-object)
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.GetHashCode
  commentId: M:System.Object.GetHashCode
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  name: GetHashCode()
  nameWithType: object.GetHashCode()
  fullName: object.GetHashCode()
  nameWithType.vb: Object.GetHashCode()
  fullName.vb: Object.GetHashCode()
  spec.csharp:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetHashCode
    name: GetHashCode
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gethashcode
  - name: (
  - name: )
- uid: System.Object.GetType
  commentId: M:System.Object.GetType
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  name: GetType()
  nameWithType: object.GetType()
  fullName: object.GetType()
  nameWithType.vb: Object.GetType()
  fullName.vb: Object.GetType()
  spec.csharp:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.GetType
    name: GetType
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.gettype
  - name: (
  - name: )
- uid: System.Object.MemberwiseClone
  commentId: M:System.Object.MemberwiseClone
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  name: MemberwiseClone()
  nameWithType: object.MemberwiseClone()
  fullName: object.MemberwiseClone()
  nameWithType.vb: Object.MemberwiseClone()
  fullName.vb: Object.MemberwiseClone()
  spec.csharp:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.MemberwiseClone
    name: MemberwiseClone
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.memberwiseclone
  - name: (
  - name: )
- uid: System.Object.ReferenceEquals(System.Object,System.Object)
  commentId: M:System.Object.ReferenceEquals(System.Object,System.Object)
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  name: ReferenceEquals(object, object)
  nameWithType: object.ReferenceEquals(object, object)
  fullName: object.ReferenceEquals(object, object)
  nameWithType.vb: Object.ReferenceEquals(Object, Object)
  fullName.vb: Object.ReferenceEquals(Object, Object)
  name.vb: ReferenceEquals(Object, Object)
  spec.csharp:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
  spec.vb:
  - uid: System.Object.ReferenceEquals(System.Object,System.Object)
    name: ReferenceEquals
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.referenceequals
  - name: (
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: ','
  - name: " "
  - uid: System.Object
    name: Object
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object
  - name: )
- uid: System.Object.ToString
  commentId: M:System.Object.ToString
  parent: System.Object
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  name: ToString()
  nameWithType: object.ToString()
  fullName: object.ToString()
  nameWithType.vb: Object.ToString()
  fullName.vb: Object.ToString()
  spec.csharp:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
  spec.vb:
  - uid: System.Object.ToString
    name: ToString
    isExternal: true
    href: https://learn.microsoft.com/dotnet/api/system.object.tostring
  - name: (
  - name: )
- uid: System
  commentId: N:System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system
  name: System
  nameWithType: System
  fullName: System
- uid: Unsplasharp.Models.Category.Id*
  commentId: Overload:Unsplasharp.Models.Category.Id
  href: Unsplasharp.Models.Category.html#Unsplasharp_Models_Category_Id
  name: Id
  nameWithType: Category.Id
  fullName: Unsplasharp.Models.Category.Id
- uid: System.String
  commentId: T:System.String
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.string
  name: string
  nameWithType: string
  fullName: string
  nameWithType.vb: String
  fullName.vb: String
  name.vb: String
- uid: Unsplasharp.Models.Category.Title*
  commentId: Overload:Unsplasharp.Models.Category.Title
  href: Unsplasharp.Models.Category.html#Unsplasharp_Models_Category_Title
  name: Title
  nameWithType: Category.Title
  fullName: Unsplasharp.Models.Category.Title
- uid: Unsplasharp.Models.Category.PhotoCount*
  commentId: Overload:Unsplasharp.Models.Category.PhotoCount
  href: Unsplasharp.Models.Category.html#Unsplasharp_Models_Category_PhotoCount
  name: PhotoCount
  nameWithType: Category.PhotoCount
  fullName: Unsplasharp.Models.Category.PhotoCount
- uid: System.Int32
  commentId: T:System.Int32
  parent: System
  isExternal: true
  href: https://learn.microsoft.com/dotnet/api/system.int32
  name: int
  nameWithType: int
  fullName: int
  nameWithType.vb: Integer
  fullName.vb: Integer
  name.vb: Integer
- uid: Unsplasharp.Models.Category.Links*
  commentId: Overload:Unsplasharp.Models.Category.Links
  href: Unsplasharp.Models.Category.html#Unsplasharp_Models_Category_Links
  name: Links
  nameWithType: Category.Links
  fullName: Unsplasharp.Models.Category.Links
- uid: Unsplasharp.Models.CategoryLinks
  commentId: T:Unsplasharp.Models.CategoryLinks
  parent: Unsplasharp.Models
  href: Unsplasharp.Models.CategoryLinks.html
  name: CategoryLinks
  nameWithType: CategoryLinks
  fullName: Unsplasharp.Models.CategoryLinks
